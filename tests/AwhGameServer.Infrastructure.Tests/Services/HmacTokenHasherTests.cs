using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using FluentAssertions;
using AwhGameServer.Infrastructure.Configurations;
using AwhGameServer.Infrastructure.Services;

namespace AwhGameServer.Infrastructure.Tests.Services;

public class HmacTokenHasherTests
{
    private const string ValidPepperBase64 = "dGVzdC1wZXBwZXItZm9yLWhtYWMtdG9rZW4taGFzaGVyLXRlc3RzLTEyMzQ1Njc4OTA=";

    [Fact(DisplayName = "HashToken с валидным токеном возвращает корректный хеш")]
    public async Task HashToken_WithValidToken_ReturnsCorrectHash()
    {
        // Arrange
        var hasher = CreateHasher(ValidPepperBase64);
        const string token = "test-token-123";

        // Act
        var result = await hasher.HashToken(token, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.HashBase64Url.Should().NotBeNullOrEmpty();
        result.HashBase64Url.Should().NotContain("+");
        result.HashBase64Url.Should().NotContain("/");
        result.HashBase64Url.Should().NotContain("=");
    }

    [Fact(DisplayName = "HashToken с одинаковыми токенами возвращает одинаковые хеши")]
    public async Task HashToken_WithSameTokens_ReturnsSameHashes()
    {
        // Arrange
        var hasher = CreateHasher(ValidPepperBase64);
        const string token = "identical-token";

        // Act
        var hash1 = await hasher.HashToken(token, CancellationToken.None);
        var hash2 = await hasher.HashToken(token, CancellationToken.None);

        // Assert
        hash1.HashBase64Url.Should().Be(hash2.HashBase64Url);
    }

    [Fact(DisplayName = "HashToken с разными токенами возвращает разные хеши")]
    public async Task HashToken_WithDifferentTokens_ReturnsDifferentHashes()
    {
        // Arrange
        var hasher = CreateHasher(ValidPepperBase64);
        const string token1 = "token-one";
        const string token2 = "token-two";

        // Act
        var hash1 = await hasher.HashToken(token1, CancellationToken.None);
        var hash2 = await hasher.HashToken(token2, CancellationToken.None);

        // Assert
        hash1.HashBase64Url.Should().NotBe(hash2.HashBase64Url);
    }

    [Fact(DisplayName = "HashToken с пустым токеном возвращает валидный хеш")]
    public async Task HashToken_WithEmptyToken_ReturnsValidHash()
    {
        // Arrange
        var hasher = CreateHasher(ValidPepperBase64);
        const string emptyToken = "";

        // Act
        var result = await hasher.HashToken(emptyToken, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.HashBase64Url.Should().NotBeNullOrEmpty();
    }

    [Fact(DisplayName = "HashToken с null токеном выбрасывает ArgumentNullException")]
    public async Task HashToken_WithNullToken_ThrowsArgumentNullException()
    {
        // Arrange
        var hasher = CreateHasher(ValidPepperBase64);

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => hasher.HashToken(null!, CancellationToken.None));
    }

    [Fact(DisplayName = "HashToken с CancellationToken работает корректно")]
    public async Task HashToken_WithCancellationToken_WorksCorrectly()
    {
        // Arrange
        var hasher = CreateHasher(ValidPepperBase64);
        const string token = "test-token";
        using var cts = new CancellationTokenSource();

        // Act
        var result = await hasher.HashToken(token, cts.Token);

        // Assert
        result.Should().NotBeNull();
        result.HashBase64Url.Should().NotBeNullOrEmpty();
    }

    [Fact(DisplayName = "Конструктор с валидной конфигурацией создаёт экземпляр успешно")]
    public void Constructor_WithValidConfig_CreatesInstanceSuccessfully()
    {
        // Arrange & Act
        var hasher = CreateHasher(ValidPepperBase64);

        // Assert
        hasher.Should().NotBeNull();
    }

    [Fact(DisplayName = "Конструктор с невалидной Base64 строкой выбрасывает FormatException")]
    public void Constructor_WithInvalidBase64_ThrowsFormatException()
    {
        // Arrange
        const string invalidBase64 = "invalid-base64-string!@#";
        var config = CreateConfig(invalidBase64);
        var loggerMock = new Mock<ILogger<HmacTokenHasher>>();

        // Act & Assert
        Assert.Throws<FormatException>(() => new HmacTokenHasher(config, loggerMock.Object));
    }

    [Fact(DisplayName = "HashToken возвращает Base64Url кодированную строку")]
    public async Task HashToken_ReturnsBase64UrlEncodedString()
    {
        // Arrange
        var hasher = CreateHasher(ValidPepperBase64);
        const string token = "test-token-with-special-chars-+/=";

        // Act
        var result = await hasher.HashToken(token, CancellationToken.None);

        // Assert
        result.HashBase64Url.Should().MatchRegex("^[A-Za-z0-9_-]+$");
    }

    [Fact(DisplayName = "HashToken с разными pepper возвращает разные хеши")]
    public async Task HashToken_WithDifferentPeppers_ReturnsDifferentHashes()
    {
        // Arrange
        const string pepper1 = "dGVzdC1wZXBwZXItb25lLWZvci1obWFjLXRva2VuLWhhc2hlci10ZXN0cy0xMjM0NTY3ODkw";
        const string pepper2 = "dGVzdC1wZXBwZXItdHdvLWZvci1obWFjLXRva2VuLWhhc2hlci10ZXN0cy0wOTg3NjU0MzIx";

        var hasher1 = CreateHasher(pepper1);
        var hasher2 = CreateHasher(pepper2);
        
        const string token = "same-token";

        // Act
        var hash1 = await hasher1.HashToken(token, CancellationToken.None);
        var hash2 = await hasher2.HashToken(token, CancellationToken.None);

        // Assert
        hash1.HashBase64Url.Should().NotBe(hash2.HashBase64Url);
    }

    [Theory(DisplayName = "HashToken с различными токенами работает корректно")]
    [InlineData("simple-token")]
    [InlineData("token-with-numbers-123456")]
    [InlineData("token_with_underscores")]
    [InlineData("token-with-special-chars-!@#$%^&*()")]
    [InlineData("очень-длинный-токен-с-русскими-символами-и-различными-знаками")]
    [InlineData("🚀🎉💻 emoji token")]
    public async Task HashToken_WithVariousTokens_WorksCorrectly(string token)
    {
        // Arrange
        var hasher = CreateHasher(ValidPepperBase64);

        // Act
        var result = await hasher.HashToken(token, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.HashBase64Url.Should().NotBeNullOrEmpty();
        result.HashBase64Url.Should().MatchRegex("^[A-Za-z0-9_-]+$");
    }

    [Fact(DisplayName = "HashToken возвращает хеш ожидаемой длины")]
    public async Task HashToken_ReturnsExpectedHashLength()
    {
        // Arrange
        var hasher = CreateHasher(ValidPepperBase64);
        const string token = "test-token";

        // Act
        var result = await hasher.HashToken(token, CancellationToken.None);

        // Assert
        var hashBytes = Convert.FromBase64String(AddBase64Padding(result.HashBase64Url.Replace('-', '+').Replace('_', '/')));
        hashBytes.Length.Should().Be(32); // HMACSHA256 produces 32 bytes
    }

    private static IOptions<HmacTokenHasherConfig> CreateConfig(string pepperBase64)
    {
        var config = new HmacTokenHasherConfig
        {
            PepperBase64 = pepperBase64
        };
        return Options.Create(config);
    }

    private static HmacTokenHasher CreateHasher(string pepperBase64)
    {
        var config = CreateConfig(pepperBase64);
        var loggerMock = new Mock<ILogger<HmacTokenHasher>>();
        return new HmacTokenHasher(config, loggerMock.Object);
    }

    private static string AddBase64Padding(string base64)
    {
        var padding = 4 - (base64.Length % 4);
        if (padding == 4) return base64;
        return base64 + new string('=', padding);
    }
}
