using Microsoft.Extensions.Logging;
using Moq;
using AwhGameServer.Application.Models;
using AwhGameServer.Domain.ValueObjects.Ids.Users;
using AwhGameServer.Infrastructure.Stores;
using FluentAssertions;

namespace AwhGameServer.Infrastructure.Tests.Stores;

/// <summary>
/// Тесты thread-safety для InMemoryAuthSessionStore.
/// Проверяют корректность работы в многопоточной среде при использовании как Singleton.
/// </summary>
public class InMemoryAuthSessionStoreThreadSafetyTests : IDisposable
{
    private readonly InMemoryAuthSessionStore _store;
    private readonly Mock<ILogger<InMemoryAuthSessionStore>> _loggerMock;

    public InMemoryAuthSessionStoreThreadSafetyTests()
    {
        _loggerMock = new Mock<ILogger<InMemoryAuthSessionStore>>();
        _store = new InMemoryAuthSessionStore(_loggerMock.Object);
    }

    public void Dispose()
    {
        _store?.Dispose();
    }

    [Fact(DisplayName = "Параллельное создание сессий для разных пользователей работает корректно")]
    public async Task ConcurrentSessionCreation_ForDifferentUsers_WorksCorrectly()
    {
        // Arrange
        const int userCount = 10;
        const int sessionsPerUser = 5;
        var tasks = new List<Task>();
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        // Act - создаем сессии параллельно для разных пользователей
        for (int userId = 0; userId < userCount; userId++)
        {
            var currentUserId = new UserId($"user-{userId}");
            
            for (int sessionIndex = 0; sessionIndex < sessionsPerUser; sessionIndex++)
            {
                var sessionId = $"session-{userId}-{sessionIndex}";
                var refreshHash = new TokenHash($"hash-{userId}-{sessionIndex}");
                
                tasks.Add(_store.RevokeAllUserSessionsThenCreateAsync(
                    sessionId, currentUserId, refreshHash, authIdentityId, expiresAt, CancellationToken.None));
            }
        }

        // Assert - все операции должны завершиться без исключений
        await Task.WhenAll(tasks);

        // Проверяем, что для каждого пользователя осталась только последняя сессия
        for (int userId = 0; userId < userCount; userId++)
        {
            var lastSessionId = $"session-{userId}-{sessionsPerUser - 1}";
            var session = await _store.GetSessionAsync(lastSessionId, CancellationToken.None);
            session.Should().NotBeNull($"последняя сессия для пользователя {userId} должна существовать");
            
            // Проверяем, что предыдущие сессии удалены
            for (int sessionIndex = 0; sessionIndex < sessionsPerUser - 1; sessionIndex++)
            {
                var oldSessionId = $"session-{userId}-{sessionIndex}";
                var oldSession = await _store.GetSessionAsync(oldSessionId, CancellationToken.None);
                oldSession.Should().BeNull($"старая сессия {oldSessionId} должна быть удалена");
            }
        }
    }

    [Fact(DisplayName = "Параллельное чтение и обновление сессий работает корректно")]
    public async Task ConcurrentReadAndUpdate_WorksCorrectly()
    {
        // Arrange
        const int operationCount = 100;
        var userId = new UserId("test-user");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);
        
        // Создаем начальную сессию
        var initialSessionId = "initial-session";
        var initialRefreshHash = new TokenHash("initial-hash");
        await _store.RevokeAllUserSessionsThenCreateAsync(
            initialSessionId, userId, initialRefreshHash, authIdentityId, expiresAt, CancellationToken.None);

        var readTasks = new List<Task<AuthSession?>>();
        var updateTasks = new List<Task<bool>>();

        // Act - параллельно читаем и обновляем сессию
        for (int i = 0; i < operationCount; i++)
        {
            // Задачи чтения
            readTasks.Add(_store.GetSessionAsync(initialSessionId, CancellationToken.None));
            readTasks.Add(_store.GetSessionByRefreshHashAsync(initialRefreshHash, CancellationToken.None));
            
            // Задачи обновления
            var newRefreshHash = new TokenHash($"new-hash-{i}");
            var newExpiresAt = DateTimeOffset.UtcNow.AddHours(2);
            updateTasks.Add(_store.TryRotateRefreshAsync(initialRefreshHash, newRefreshHash, newExpiresAt, CancellationToken.None));
        }

        // Assert - все операции должны завершиться без исключений
        var readResults = await Task.WhenAll(readTasks);
        var updateResults = await Task.WhenAll(updateTasks);

        // Проверяем, что чтения не вернули null (кроме случаев, когда сессия была обновлена)
        readResults.Should().NotBeEmpty();
        
        // Проверяем, что хотя бы некоторые обновления прошли успешно
        updateResults.Should().Contain(true);
    }

    [Fact(DisplayName = "Параллельная ротация токенов одной сессии работает корректно")]
    public async Task ConcurrentTokenRotation_ForSameSession_WorksCorrectly()
    {
        // Arrange
        const int rotationCount = 50;
        var userId = new UserId("test-user");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);
        
        // Создаем начальную сессию
        var sessionId = "rotation-test-session";
        var currentRefreshHash = new TokenHash("initial-hash");
        await _store.RevokeAllUserSessionsThenCreateAsync(
            sessionId, userId, currentRefreshHash, authIdentityId, expiresAt, CancellationToken.None);

        var rotationTasks = new List<Task<bool>>();
        var successfulRotations = 0;

        // Act - пытаемся ротировать токен параллельно
        for (int i = 0; i < rotationCount; i++)
        {
            var newRefreshHash = new TokenHash($"rotated-hash-{i}");
            var newExpiresAt = DateTimeOffset.UtcNow.AddHours(2);
            
            rotationTasks.Add(Task.Run(async () =>
            {
                var result = await _store.TryRotateRefreshAsync(currentRefreshHash, newRefreshHash, newExpiresAt, CancellationToken.None);
                if (result)
                {
                    Interlocked.Increment(ref successfulRotations);
                }
                return result;
            }));
        }

        var results = await Task.WhenAll(rotationTasks);

        // Assert - только одна ротация должна быть успешной (первая)
        results.Count(r => r).Should().Be(1, "только одна ротация должна быть успешной");
        successfulRotations.Should().Be(1, "счетчик успешных ротаций должен быть равен 1");
        
        // Проверяем, что сессия все еще существует
        var finalSession = await _store.GetSessionAsync(sessionId, CancellationToken.None);
        finalSession.Should().NotBeNull("сессия должна существовать после ротации");
    }

    [Fact(DisplayName = "Параллельное создание сессий с коллизией sessionId выбрасывает исключения корректно")]
    public async Task ConcurrentSessionCreation_WithSessionIdCollision_ThrowsExceptionsCorrectly()
    {
        // Arrange
        const int attemptCount = 10;
        var collisionSessionId = "collision-session";
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        var tasks = new List<Task>();
        var exceptions = new List<Exception>();
        var successCount = 0;

        // Act - пытаемся создать сессии с одинаковым sessionId для разных пользователей
        for (int i = 0; i < attemptCount; i++)
        {
            var userId = new UserId($"user-{i}");
            var refreshHash = new TokenHash($"hash-{i}");
            
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    await _store.RevokeAllUserSessionsThenCreateAsync(
                        collisionSessionId, userId, refreshHash, authIdentityId, expiresAt, CancellationToken.None);
                    Interlocked.Increment(ref successCount);
                }
                catch (Exception ex)
                {
                    lock (exceptions)
                    {
                        exceptions.Add(ex);
                    }
                }
            }));
        }

        await Task.WhenAll(tasks);

        // Assert - только одна операция должна быть успешной, остальные должны выбросить исключения
        successCount.Should().Be(1, "только одна операция создания сессии должна быть успешной");
        exceptions.Should().HaveCount(attemptCount - 1, "остальные операции должны выбросить исключения");
        exceptions.Should().AllBeOfType<InvalidOperationException>("все исключения должны быть InvalidOperationException");
        
        // Проверяем, что сессия была создана
        var session = await _store.GetSessionAsync(collisionSessionId, CancellationToken.None);
        session.Should().NotBeNull("сессия должна быть создана");
    }

    [Fact(DisplayName = "Параллельное чтение истекших сессий работает корректно")]
    public async Task ConcurrentReadingOfExpiredSessions_WorksCorrectly()
    {
        // Arrange
        const int readerCount = 20;
        var userId = new UserId("test-user");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiredTime = DateTime.UtcNow.AddHours(-1); // Истекшая сессия
        
        // Создаем истекшую сессию
        var sessionId = "expired-session";
        var refreshHash = new TokenHash("expired-hash");
        await _store.RevokeAllUserSessionsThenCreateAsync(
            sessionId, userId, refreshHash, authIdentityId, expiredTime, CancellationToken.None);

        var readTasks = new List<Task<AuthSession?>>();

        // Act - параллельно читаем истекшую сессию
        for (int i = 0; i < readerCount; i++)
        {
            readTasks.Add(_store.GetSessionAsync(sessionId, CancellationToken.None));
            readTasks.Add(_store.GetSessionByRefreshHashAsync(refreshHash, CancellationToken.None));
        }

        var results = await Task.WhenAll(readTasks);

        // Assert - все чтения должны вернуть null (сессия истекла и удалена)
        results.Should().AllSatisfy(result => result.Should().BeNull("все чтения истекшей сессии должны вернуть null"));
        
        // Проверяем, что сессия действительно удалена
        var finalCheck = await _store.GetSessionAsync(sessionId, CancellationToken.None);
        finalCheck.Should().BeNull("сессия должна быть удалена после истечения");
    }

    [Fact(DisplayName = "Массовые параллельные операции не вызывают deadlock")]
    public async Task MassiveConcurrentOperations_DoNotCauseDeadlock()
    {
        // Arrange
        const int operationCount = 200;
        var tasks = new List<Task>();
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        // Act - выполняем множество разных операций параллельно
        for (int i = 0; i < operationCount; i++)
        {
            var userId = new UserId($"user-{i % 10}"); // 10 разных пользователей
            var sessionId = $"session-{i}";
            var refreshHash = new TokenHash($"hash-{i}");
            
            // Создание сессий
            tasks.Add(_store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, refreshHash, authIdentityId, expiresAt, CancellationToken.None));
            
            // Чтение сессий
            tasks.Add(_store.GetSessionAsync(sessionId, CancellationToken.None));
            tasks.Add(_store.GetSessionByRefreshHashAsync(refreshHash, CancellationToken.None));
            
            // Ротация токенов
            var newRefreshHash = new TokenHash($"new-hash-{i}");
            tasks.Add(_store.TryRotateRefreshAsync(refreshHash, newRefreshHash, DateTimeOffset.UtcNow.AddHours(2), CancellationToken.None));
        }

        // Assert - все операции должны завершиться в разумное время без deadlock
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
        var allTasksTask = Task.WhenAll(tasks);
        var completedTask = await Task.WhenAny(allTasksTask, Task.Delay(Timeout.Infinite, cts.Token));

        completedTask.Should().Be(allTasksTask, "все операции должны завершиться без deadlock");
        allTasksTask.IsCompletedSuccessfully.Should().BeTrue("все операции должны завершиться успешно");
    }
}
