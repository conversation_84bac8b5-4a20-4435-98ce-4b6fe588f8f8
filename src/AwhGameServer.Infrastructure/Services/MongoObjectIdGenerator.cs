using MongoDB.Bson;
using AwhGameServer.Domain.ValueObjects.Ids;
using AwhGameServer.Application.Abstractions.Services;

namespace AwhGameServer.Infrastructure.Services;

/// <summary>
/// Реализация <see cref="ITypedIdGenerator{TId}"/> для генерации идентификаторов MongoDB.
/// </summary>
public class MongoObjectIdGenerator<T> : ITypedIdGenerator<T> where T : TypedId
{
    public Task<T> New(CancellationToken ct)
    {
        var idValue = ObjectId.GenerateNewId().ToString();
        
        var typedId = (T)Activator.CreateInstance(typeof(T), idValue)!;
        
        return Task.FromResult(typedId);
    }
}
