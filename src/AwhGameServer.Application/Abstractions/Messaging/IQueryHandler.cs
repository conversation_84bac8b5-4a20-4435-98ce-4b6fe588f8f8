using MediatR;

namespace AwhGameServer.Application.Abstractions.Messaging;

/// <summary>
/// Контракт обработчика запроса в паттерне CQRS.
/// </summary>
///
/// <typeparam name="TQuery">Тип запроса, который обрабатывает данный обработчик.</typeparam>
/// <typeparam name="TResponse">Тип результата, возвращаемого после выполнения запроса.</typeparam>
///
/// <remarks>
/// Обработчик инкапсулирует логику получения данных из домена или внешних источников,
/// не изменяя состояние системы.
/// </remarks>
public interface IQueryHandler<in TQuery, TResponse> : IRequestHandler<TQuery, TResponse>
    where TQuery : IQuery<TResponse>
{
}
