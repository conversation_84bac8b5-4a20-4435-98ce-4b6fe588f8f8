using MediatR;

namespace AwhGameServer.Application.Abstractions.Messaging;

/// <summary>
/// Маркерный интерфейс команды в паттерне CQRS.
/// </summary>
///
/// <typeparam name="TResponse">Тип результата, возвращаемого после выполнения команды.</typeparam>
///
/// <remarks>
/// Команды изменяют состояние системы.
/// Используется вместе с <see cref="ICommandHandler{TCommand,TResponse}"/> и медиатором (например, MediatR).
/// </remarks>
public interface ICommand<out TResponse> : IRequest<TResponse>;
