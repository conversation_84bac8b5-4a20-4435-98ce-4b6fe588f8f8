using AwhGameServer.Domain.ValueObjects.Ids.Users;
using AwhGameServer.Application.Models;

namespace AwhGameServer.Application.Abstractions.Services;

/// <summary>
/// Сервис для генерации токенов аутентификации.
/// Также хранит в себе логику вычисления времени жизни токенов.
/// </summary>
public interface IAuthTokensGenerator
{
    public Task<AuthTokens> GenerateAuthTokens(string sessionId, UserId userId, CancellationToken ct);
}
