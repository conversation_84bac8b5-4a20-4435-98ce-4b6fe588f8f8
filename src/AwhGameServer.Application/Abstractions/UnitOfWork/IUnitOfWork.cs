namespace AwhGameServer.Application.Abstractions.UnitOfWork;

/// <summary>
/// Маркерный интерфейс для единиц работы с базой данных.
/// UnitOfWork - объект, инкапсулирующий логику транзакционного доступа к данным.
/// Под транзакцией подразумевается группа операций, которые должны быть выполнены успешно или не выполнены вообще.
/// Под конкретный UseCase создается свой UnitOfWork, в которой объединяются все необходимые репозитории.
/// </summary>
public interface IUnitOfWork
{
    /// <summary>
    /// Сохраняет все изменения, внесенные в репозиториях.
    /// Если при сохранении одного из репозиториев произошла ошибка,
    /// изменения не сохраняются во всех репозиториях.
    /// </summary>
    Task SaveChangesAsync(CancellationToken ct);
}
