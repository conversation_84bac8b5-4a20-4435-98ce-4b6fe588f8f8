using Microsoft.AspNetCore.Mvc;
using MediatR;
using AwhGameServer.Application.Exceptions;
using AwhGameServer.Contracts.Api.V3.User.Authentication;
using AwhGameServer.WebApi.Mappings.Contracts.Api.V3.User.Authentication;

namespace AwhGameServer.WebApi.Controllers.Api.V3.User;

/// <summary>
/// Контроллер для обработки запросов аутентификации пользователей.
/// Предоставляет API для входа в систему и обновления токенов доступа.
/// </summary>
[ApiController]
[Route("api/v3/user/authentication")]
public class AuthenticationController(ILogger<AuthenticationController> logger, IMediator mediator) : Controller
{
    /// <summary>
    /// Выполняет аутентификацию пользователя на основе предоставленных учетных данных.
    /// </summary>
    /// 
    /// <param name="request">Запрос на вход, содержащий идентификационные данные пользователя</param>
    /// <param name="ct">Токен отмены операции</param>
    /// 
    /// <returns>Результат аутентификации с токенами доступа</returns>
    /// 
    /// <response code="200">Успешная аутентификация</response>
    /// <response code="400">Некорректные данные запроса</response>
    /// <response code="401">Неверные учетные данные</response>
    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginRequest request, CancellationToken ct)
    {
        logger.LogInformation("Получен запрос на аутентификацию для методов: {AuthMethods}",
            string.Join(", ", request.AuthIdentities.Select(x => x.AuthMethod)));

        try
        {
            var loginCommand = request.MapToApplicationCommand();

            var loginCommandResult = await mediator.Send(loginCommand, ct);

            var response = loginCommandResult.MapToContract();

            logger.LogInformation("Запрос на аутентификацию успешно выполнен для пользователя {UserId}",
                loginCommandResult.UserId);

            return Ok(response);
        }
        catch (AuthenticationException ex)
        {
            logger.LogWarning(ex, "Ошибка при выполнении запроса на аутентификацию {Message}", ex.Message);
            return Unauthorized(ex.Message);
        }
        catch (ApplicationArgumentException ex)
        {
            logger.LogWarning(ex, "Ошибка при выполнении запроса на аутентификацию {Message}", ex.Message);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Ошибка при выполнении запроса на аутентификацию {Message}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Обновляет токены доступа пользователя на основе refresh token.
    /// </summary>
    /// 
    /// <param name="request">Запрос на обновление токенов, содержащий refresh token</param>
    /// <param name="ct"></param>
    /// 
    /// <returns>Новые токены доступа</returns>
    /// 
    /// <response code="200">Успешное обновление токенов</response>
    /// <response code="400">Некорректные данные запроса</response>
    /// <response code="401">Недействительный refresh token</response>
    [HttpPost("refresh")]
    public async Task<IActionResult> Refresh([FromBody] RefreshRequest request, CancellationToken ct)
    {
        logger.LogInformation("Получен запрос на обновление токенов");

        try
        {
            var refreshCommand = request.MapToApplicationCommand();

            var refreshCommandResult = await mediator.Send(refreshCommand, ct);

            var response = refreshCommandResult.MapToContract();

            logger.LogInformation("Запрос на обновление токенов успешно выполнен для пользователя {UserId}",
                refreshCommandResult.UserId);
            return Ok(response);
        }
        catch (AuthenticationException ex)
        {
            logger.LogWarning(ex, "Ошибка при выполнении запроса на обновление токенов {Message}", ex.Message);
            return Unauthorized(ex.Message);
        }
        catch (ApplicationArgumentException ex)
        {
            logger.LogWarning(ex, "Ошибка при выполнении запроса на обновление токенов {Message}", ex.Message);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Ошибка при выполнении запроса на обновление токенов {Message}", ex.Message);
            throw;
        }
    }
}
