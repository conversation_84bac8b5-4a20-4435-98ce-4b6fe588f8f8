<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <PreserveCompilationContext>true</PreserveCompilationContext>
        <CopyOutputSymbolsToPublishDirectory>true</CopyOutputSymbolsToPublishDirectory>
        <CopyOutputSymbolsToBuildDirectory>true</CopyOutputSymbolsToBuildDirectory>
    </PropertyGroup>

    <ItemGroup>
      <Content Include="..\..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="FluentValidation" Version="12.0.0" />
      <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
      <PackageReference Include="MediatR" Version="13.0.0" />
      <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.7" />
      <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
      <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
      <PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
      <PackageReference Include="SharpGrip.FluentValidation.AutoValidation.Mvc" Version="1.5.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\AwhGameServer.Application\AwhGameServer.Application.csproj" />
      <ProjectReference Include="..\AwhGameServer.Contracts\AwhGameServer.Contracts.csproj" />
      <ProjectReference Include="..\AwhGameServer.Infrastructure\AwhGameServer.Infrastructure.csproj" />
    </ItemGroup>

</Project>
